from typing import List, Optional, Dict, Any
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import ReturnDocument
from app.config.settings import settings
from app.models.conversation import Conversation, Message, PyObjectId
from app.config.logger import log_info, log_error
from fastapi import Request
from bson.regex import Regex
import time
class MemoryService:
    _client_pool = None
    _db_pool = None

    def __init__(self, req: Request):
        self.client = None
        self.db = None
        self.conversations = None
        self.messages = None
        self.req = req

    @classmethod
    async def get_shared_client(cls):
        """Get or create shared MongoDB client with connection pooling"""
        if cls._client_pool is None:
            cls._client_pool = AsyncIOMotorClient(
                settings.MONGODB_URI,
                maxPoolSize=200,
                minPoolSize=20,
                maxIdleTimeMS=30000,
                waitQueueTimeoutMS=5000,
                serverSelectionTimeoutMS=5000
            )

        return cls._client_pool

    @classmethod
    async def get_shared_database(cls):
        """Get or create shared MongoDB database connection"""
        if cls._db_pool is None:
            client = await cls.get_shared_client()
            cls._db_pool = client.get_database(settings.MONGODB_DATABASE)

        return cls._db_pool

    @classmethod
    async def close_shared_pools(cls):
        """Close shared connection pools - for application shutdown"""
        try:
            if cls._client_pool:
                cls._client_pool.close()
                cls._client_pool = None
                cls._db_pool = None
                print("MemoryService: MongoDB connection pools closed.")
        except Exception as e:
            print(f"Error closing MemoryService MongoDB pools: {e}")

    async def connect(self):
        """Initialize MongoDB connection using shared connection pool"""
        try:
            connect_start_time = time.time()
            log_info(self.req, "[TIMING] MemoryService.connect: Starting MongoDB connection")

            # Use shared connection pool
            self.db = await self.get_shared_database()
            self.client = await self.get_shared_client()
            self.conversations = self.db.conversations
            self.messages = self.db.messages

            connect_elapsed = time.time() - connect_start_time
            log_info(self.req, f"[TIMING] MemoryService.connect: MongoDB connection completed - Elapsed: {connect_elapsed:.3f}s")
            log_info(self.req, "MongoDB connection established using shared pool.")
        except Exception as e:
            log_error(self.req, f"Failed to connect to MongoDB: {e}")
            raise

    async def create_conversation(self, user_id: int, title: str) -> Conversation:
        """Create a new conversation"""
        try:
            create_conv_start_time = time.time()
            log_info(self.req, "[TIMING] MemoryService.create_conversation: Starting conversation creation")

            conversation = Conversation(
                user_id=user_id,
                title=title
            )
            result = await self.conversations.insert_one(conversation.dict(by_alias=True))
            conversation.id = result.inserted_id

            create_conv_elapsed = time.time() - create_conv_start_time
            log_info(self.req, f"[TIMING] MemoryService.create_conversation: Conversation creation completed - Elapsed: {create_conv_elapsed:.3f}s")
            log_info(self.req, f"Conversation created with ID: {conversation.id}")
            return conversation
        except Exception as e:
            log_error(self.req, f"Failed to create conversation: {e}")
            raise

    async def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Get a conversation by ID"""
        try:
            get_conv_start_time = time.time()
            log_info(self.req, "[TIMING] MemoryService.get_conversation: Starting conversation retrieval")

            conversation = await self.conversations.find_one({"_id": PyObjectId(conversation_id)})

            get_conv_elapsed = time.time() - get_conv_start_time
            log_info(self.req, f"[TIMING] MemoryService.get_conversation: Conversation retrieval completed - Elapsed: {get_conv_elapsed:.3f}s")

            if conversation:
                log_info(self.req, f"Fetched conversation with ID: {conversation_id}")
                return Conversation(**conversation)
        except Exception as e:
            log_error(self.req, f"Failed to get conversation: {e}")
        return None

    async def get_user_conversations(self, user_id: int, page_size: int = 10, page: int = 1) -> Dict[str, Any]:
        """Get all conversations for a user with optimized query and total count"""
        try:
            skip = (page - 1) * page_size

            # Get total count of conversations for the user
            total_conversation_count = await self.conversations.count_documents({
                "user_id": user_id,
                "deleted_at": None
            })

            # Use aggregation pipeline for better performance
            pipeline = [
                {
                    "$match": {
                        "user_id": user_id,
                        "deleted_at": None
                    }
                },
                {"$sort": {"updated_at": -1}},
                {"$skip": skip},
                {"$limit": page_size}
            ]
            cursor = self.conversations.aggregate(pipeline)
            conversations = [Conversation(**doc) async for doc in cursor]

            # Return response with meaningful total count key
            result = {
                "conversations": conversations,
                "total_conversation_count": total_conversation_count
            }

            log_info(self.req, f"Retrieved {len(conversations)} conversations for user {user_id}, total: {total_conversation_count}")
            return result
        except Exception as e:
            log_error(self.req, f"Failed to fetch conversations for user {user_id}: {e}")
            # Return empty response on error
            return {
                "conversations": [],
                "total_conversation_count": 0
            }

    async def add_message(self, conversation_id: str, user_id: int, content: str, role: str,
                         inventory_vehicles: List[Dict[str, Any]] = None,
                         is_inventory_fetch: int = None, total_vehicle_count: int = 0) -> Message:
        """Add a message to a conversation. Creates new conversation if ID not provided."""
        try:
            # Start overall timing for message addition
            add_message_start_time = time.time()
            log_info(self.req, "[TIMING] MemoryService.add_message: Starting message addition process")

            # Time conversation management
            conversation_mgmt_start_time = time.time()
            log_info(self.req, "[TIMING] MemoryService.conversation_management: Starting conversation validation/creation")

            if not conversation_id:
                conversation = await self.create_conversation(user_id=user_id, title=content[:50])
                conversation_id = str(conversation.id)
                log_info(self.req, f"No conversation ID provided. New conversation created with ID: {conversation_id}")
            else:
                conversation = await self.get_conversation(conversation_id)
                if not conversation:
                    conversation = await self.create_conversation(user_id=user_id, title=content[:50])
                    conversation_id = str(conversation.id)
                    log_info(self.req, f"Invalid conversation ID provided. Created new conversation: {conversation_id}")

            conversation_mgmt_elapsed = time.time() - conversation_mgmt_start_time
            log_info(self.req, f"[TIMING] MemoryService.conversation_management: Conversation validation/creation completed - Elapsed: {conversation_mgmt_elapsed:.3f}s")

            # Time message creation and database operations
            message_db_start_time = time.time()
            log_info(self.req, "[TIMING] MemoryService.message_database_operations: Starting message creation and database operations")

            message = Message(
                conversation_id=PyObjectId(conversation_id),
                user_id=user_id,
                content=content,
                role=role,
                inventory_vehicles=inventory_vehicles,
                is_inventory_fetch=is_inventory_fetch,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                total_vehicle_count=total_vehicle_count
            )
            result = await self.messages.insert_one(message.dict(by_alias=True))
            message.id = result.inserted_id

            await self.conversations.update_one(
                {"_id": PyObjectId(conversation_id)},
                {"$set": {"updated_at": datetime.utcnow()}}
            )

            message_db_elapsed = time.time() - message_db_start_time
            log_info(self.req, f"[TIMING] MemoryService.message_database_operations: Message creation and database operations completed - Elapsed: {message_db_elapsed:.3f}s")

            # Log overall completion timing
            add_message_elapsed = time.time() - add_message_start_time
            log_info(self.req, f"[TIMING] MemoryService.add_message: Complete message addition process finished - Total Elapsed: {add_message_elapsed:.3f}s")
            log_info(self.req, f"Message added to conversation {conversation_id} with ID: {message.id}")
            return message
        except Exception as e:
            log_error(self.req, f"Failed to add message to conversation {conversation_id}: {e}")
            raise

    async def get_conversation_messages(self, conversation_id: str, page_size: int = 10, page: int = 1) -> Dict[str, Any]:
        """Get messages for a conversation (most recent first) with total count"""
        try:
            # Start timing for message retrieval
            get_messages_start_time = time.time()
            log_info(self.req, "[TIMING] MemoryService.get_conversation_messages: Starting message retrieval process")

            skip = (page - 1) * page_size

            if not conversation_id:
                log_info(self.req, "No conversation ID provided. Returning empty message list.")
                # Return empty response
                return {
                    "messages": [],
                    "total_message_count": 0
                }

            # Get total count of messages for the conversation
            total_message_count = await self.messages.count_documents({"conversation_id": PyObjectId(conversation_id)})

            # Time database query execution
            db_query_start_time = time.time()
            log_info(self.req, "[TIMING] MemoryService.message_query: Starting message database query")

            cursor = self.messages.find({"conversation_id": PyObjectId(conversation_id)}) \
                .sort("created_at", -1) \
                .skip(skip) \
                .limit(page_size)

            messages = []
            async for doc in cursor:
                new_message_dict = {**doc}
                new_message_dict["message_id"] = str(doc["_id"])
                messages.append(Message(**new_message_dict))

            db_query_elapsed = time.time() - db_query_start_time
            log_info(self.req, f"[TIMING] MemoryService.message_query: Message database query completed - Elapsed: {db_query_elapsed:.3f}s")

            # Return response with meaningful total count key
            result = {
                "messages": messages,
                "total_message_count": total_message_count
            }

            # Log overall completion timing
            get_messages_elapsed = time.time() - get_messages_start_time
            log_info(self.req, f"[TIMING] MemoryService.get_conversation_messages: Complete message retrieval process finished - Total Elapsed: {get_messages_elapsed:.3f}s")
            log_info(self.req, f"Retrieved {len(messages)} messages for conversation {conversation_id}, total: {total_message_count}")
            return result

        except Exception as e:
            log_error(self.req, f"Failed to retrieve messages for conversation {conversation_id}: {e}")
            # Return empty response on error
            return {
                "messages": [],
                "total_message_count": 0
            }

    async def get_user_search_conversation(self, search: str, user_id: int, page_size: int = 10, page: int = 1) -> Dict[str, Any]:
        """Search conversations by title for a user (most recent first) with total count"""
        try:
            # Start timing for conversation search
            get_conversations_start_time = time.time()
            log_info(self.req, "[TIMING] MemoryService.get_user_search_conversation: Starting conversation search process")

            skip = (page - 1) * page_size

            if not search:
                log_info(self.req, "No search criteria found. Returning empty conversation list.")
                # Return empty response
                return {
                    "conversations": [],
                    "total_conversation_count": 0
                }

            if not user_id:
                log_info(self.req, "No user_id found. Returning empty conversation list.")
                # Return empty response
                return {
                    "conversations": [],
                    "total_conversation_count": 0
                }

            query = {
                "user_id": user_id,
                "title": {"$regex": Regex(search, "i")}
            }

            # Get total count of conversations matching the search criteria
            total_conversation_count = await self.conversations.count_documents(query)

            # Time database query execution
            db_query_start_time = time.time()
            log_info(self.req, "[TIMING] MemoryService.conversation_search_query: Starting conversation search database query")

            cursor = self.conversations.find(query) \
                .sort("updated_at", -1) \
                .skip(skip) \
                .limit(page_size)

            conversations = []
            async for doc in cursor:
                conversations.append(Conversation(**doc))

            db_query_elapsed = time.time() - db_query_start_time
            log_info(self.req, f"[TIMING] MemoryService.conversation_search_query: Conversation search database query completed - Elapsed: {db_query_elapsed:.3f}s")

            # Return response with meaningful total count key
            result = {
                "conversations": conversations,
                "total_conversation_count": total_conversation_count
            }

            # Log overall completion timing
            get_conversations_elapsed = time.time() - get_conversations_start_time
            log_info(self.req, f"[TIMING] MemoryService.get_user_search_conversation: Complete conversation search process finished - Total Elapsed: {get_conversations_elapsed:.3f}s")
            log_info(self.req, f"Retrieved {len(conversations)} conversations for search '{search}' and user_id '{user_id}', total: {total_conversation_count}")
            return result

        except Exception as e:
            log_error(self.req, f"Failed to retrieve conversations for user_id '{user_id}': {e}")
            return {
                "conversations": [],
                "total_conversation_count": 0
            }

    async def get_feedback_agent_response(self, message_id: str, feedback_flag: int, feedback_message: str) -> Optional[Message]:
        """Update feedback for a message and return the updated message"""
        try:
            feedback_data = dict()
            if feedback_flag == 1:
                feedback_data = {
                    "feedback_flag": feedback_flag,
                    "feedback_message": None,
                    "feedback_created_at": datetime.utcnow()
                }
            elif feedback_flag == 0:
                feedback_data = {
                    "feedback_flag": feedback_flag,
                    "feedback_message": None if feedback_message == "" else feedback_message,
                    "feedback_created_at": datetime.utcnow()
                }

            result = await self.messages.update_one(
                {
                    "_id": PyObjectId(message_id),
                    "role": "ai"
                },
                {"$set": feedback_data},
            )

            if result.modified_count == 0:
                log_info(self.req, f"No message updated. Either not found or data was the same. ID: {message_id}")
                return None
            else:
                log_info(self.req, f"Updated feedback for message ID: {message_id}")

            updated_message = await self.messages.find_one({"_id": PyObjectId(message_id)})
            if updated_message:
                return Message(**updated_message)

            return None

        except Exception as e:
            log_error(self.req, f"Error updating feedback for message ID {message_id}: {str(e)}")
            return None

    async def soft_delete_conversation_async(self, conversation_id: str) -> bool:
        """
        Soft delete a conversation by setting the deleted_at field to current UTC datetime.
        """
        try:
            # Validate conversation_id
            if not conversation_id:
                log_error(self.req, "No conversation ID provided for soft delete operation")
                return False

            # Update the deleted_at field with current UTC datetime
            current_utc = datetime.utcnow()
            result = await self.conversations.update_one(
                {"_id": PyObjectId(conversation_id), "deleted_at": None},
                {"$set": {"deleted_at": current_utc}}
            )

            if result.modified_count == 0:
                log_error(self.req, f"Failed to soft delete conversation. No document was modified. ID: {conversation_id}")
                return False
            else:
                log_info(self.req, f"Successfully soft deleted conversation ID: {conversation_id} at {current_utc}")
                return True

        except Exception as e:
            log_error(self.req, f"Error soft deleting conversation ID {conversation_id}: {str(e)}")
            return False


    async def close(self):
        """Close MongoDB connection - using shared pool, so just clear references"""
        try:
            close_start_time = time.time()
            log_info(self.req, "[TIMING] MemoryService.close: Starting MongoDB connection cleanup")

            # With shared connection pool, we just clear local references
            # The actual connections remain in the pool for reuse
            self.client = None
            self.db = None
            self.conversations = None
            self.messages = None

            close_elapsed = time.time() - close_start_time
            log_info(self.req, f"[TIMING] MemoryService.close: MongoDB connection cleanup completed - Elapsed: {close_elapsed:.3f}s")
            log_info(self.req, "MongoDB connection cleanup completed (shared pool).")
        except Exception as e:
            log_error(self.req, f"Error while cleaning up MongoDB connection: {e}")


    async def update_message(self, message_id: str, content: str, is_inventory_fetch: Optional[int] = None, inventory_vehicles: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Message]:
        """Update a message in a conversation"""
        try:
            update_fields = {
                "content": content,
                "is_inventory_fetch": is_inventory_fetch,
                "inventory_vehicles": inventory_vehicles,
                "updated_at": datetime.utcnow()
            }

            result = await self.messages.update_one(
                {"_id": PyObjectId(message_id), "role": "ai"},
                {"$set": update_fields}
            )

            if result.modified_count == 0:
                log_info(self.req, f"No message updated for ID: {message_id}")
                return None

            # Fetch and return the updated message
            updated_doc = await self.messages.find_one({"_id": PyObjectId(message_id)})
            if updated_doc:
                updated_message = Message(**updated_doc)
                updated_message.message_id = str(updated_doc["_id"])
                log_info(self.req, f"Message {message_id} updated successfully")
                return updated_message
            else:
                log_error(self.req, f"Could not retrieve updated message with ID: {message_id}")
                return None

        except Exception as e:
            log_error(self.req, f"Error updating message ID {message_id}: {e}")
            return None

